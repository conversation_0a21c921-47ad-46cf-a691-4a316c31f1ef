<div *ngIf="reportModel" class="w-full transition-all duration-1000 ease-out overflow-hidden"
  [ngClass]="{
         'max-h-0 opacity-0': !visible, 
         'max-h-screen opacity-100': visible
     }">
  <!-- Minimal notification header -->
  <div class="flex justify-between items-center py-1 px-2">
    <!-- Notification Message on the right side with blinking red dot -->
    <div class="flex items-center ml-auto">
      <p class="font-normal text-sm mr-2 cursor-pointer select-none" (click)="toggleExpand()"
        [innerHTML]="'SystemAdmin.Components.CustomReportEditor.CTA' | translate"> </p>
      <div class="relative">
        <!-- Blinking red dot (Record Icon) -->
        <div class="w-3 h-3 rounded-full bg-red-600 animate-ping absolute"></div>
        <div class="w-3 h-3 rounded-full bg-red-600"></div>
      </div>
    </div>
  </div>

  <!-- Expanded content (Form) -->
  <div [ngClass]="{'max-h-0 ': !isExpanded, 'max-h-screen': isExpanded}">
    <div class="p-2 mt-2">
      <!-- Tailwind Tabs Yapısı -->
      <div class="flex items-center justify-center pt-3 pb-16">
        <div class="w-2/4">
          <!-- Sekme Başlıkları -->
          <div class="flex border-b border-gray-200">
            <ng-container *ngFor="let tab of tabs; let i = index">
              <button (click)="goToTab(i)" [disabled]="!canNavigateToTab(i)" [ngClass]="{
                  'border-blue-500 text-blue-500 font-semibold': currentTab === i,
                  'opacity-50 cursor-not-allowed': !canNavigateToTab(i)
                }" class="px-4 py-2 -mb-px border-b-2 font-medium text-md focus:outline-none flex items-center">
                <fa-icon [icon]="getTabIcon(tab)" class="mr-2" size="lg"></fa-icon>
                {{ tab.label }}
              </button>
            </ng-container>
          </div>

          <!-- Form Başlangıcı -->
          <form [formGroup]="formGroup" (ngSubmit)="submitForm()">
            <!-- Sekme İçerikleri -->
            <div class="mt-4">

              <!-- Temel Bilgiler Sekmesi -->
              <div *ngIf="currentTab === 0">
                <!-- İlk Satır: Order, Name ve ColorCode -->
                <div class="grid grid-cols-1 lg:grid-cols-[auto,1fr,auto] gap-4 w-full">
                  <!-- Order -->
                  <div class="w-full lg:w-24 px-2">
                    <mat-form-field class="w-full frosty">
                      <mat-label>{{ 'SystemAdmin.Components.CustomReportEditor.Tab.Basic.Form.Order' | translate
                        }}</mat-label>
                      <input matInput type="number" formControlName="order" min="0"
                        placeholder="{{ 'SystemAdmin.Components.CustomReportEditor.Tab.Basic.Form.Order' | translate }}" />
                      <mat-error *ngIf="formGroup.get('order').hasError('required')">
                        {{ 'SystemAdmin.Common.Validation.Required' | translate }}
                      </mat-error>

                    </mat-form-field>
                  </div>

                  <!-- Name -->
                  <div class="w-full px-2">
                    <mat-form-field class="w-full frosty">
                      <mat-label>{{ 'SystemAdmin.Components.CustomReportEditor.Tab.Basic.Form.Title' | translate
                        }}</mat-label>
                      <input matInput formControlName="name"
                        placeholder="{{ 'SystemAdmin.Components.CustomReportEditor.Tab.Basic.Form.Title' | translate }}"
                        [spellcheck]="false" />
                      <mat-error *ngIf="formGroup.get('name').hasError('required')">
                        {{ 'SystemAdmin.Common.Validation.Required' | translate }}
                      </mat-error>
                      <mat-error *ngIf="formGroup.get('name').hasError('minlength')">
                        {{ 'SystemAdmin.Common.Validation.MinLength' | translate: {length:3} }}
                      </mat-error>
                      <mat-error *ngIf="formGroup.get('name').hasError('maxlength')">
                        {{ 'SystemAdmin.Common.Validation.MaxLength' | translate: {length:64} }}
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <!-- ColorCode -->
                  <div class="w-full lg:w-40 px-2">
                    <mat-form-field class="w-full frosty" ngx-colors-trigger format="hex"
                      (change)="updateColor('colorCode', $event)">
                      <mat-label>{{ 'SystemAdmin.Components.CustomReportEditor.Tab.Basic.Form.Color' | translate
                        }}</mat-label>
                      <input matInput formControlName="colorCode" class="uppercase select-none cursor-pointer"
                        readonly="true" />
                      <fa-icon matSuffix [icon]="['fas', 'circle']" size="2x" class="select-none cursor-pointer"
                        [style.color]="formGroup.get('colorCode')?.value">
                      </fa-icon>
                    </mat-form-field>
                  </div>
                </div>

                <!-- Description -->
                <div class="flex flex-wrap">
                  <div class="flex-1 px-2 mt-4 sm:mt-0">
                    <mat-form-field class="w-full frosty">
                      <mat-label>{{ 'SystemAdmin.Components.CustomReportEditor.Tab.Basic.Form.Description' | translate
                        }}</mat-label>
                      <textarea matInput formControlName="description" [spellcheck]="false" rows="2"
                        class="resize-none"></textarea>
                      <mat-error *ngIf="formGroup.get('description').hasError('required')">
                        {{ 'SystemAdmin.Common.Validation.Required' | translate }}
                      </mat-error>
                      <mat-error *ngIf="formGroup.get('description').hasError('minlength')">
                        {{ 'SystemAdmin.Common.Validation.MinLength' | translate: {length:3} }}
                      </mat-error>
                      <mat-error *ngIf="formGroup.get('description').hasError('maxlength')">
                        {{ 'SystemAdmin.Common.Validation.MaxLength' | translate: {length:256} }}
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <!-- Navigasyon Butonları -->
                <div class="flex mt-4 justify-between">



                  <ng-container *ngIf="reportModel?.new; else existingReportActionTpl">

                    <button class="ml-2 frosty red" type="button" mat-flat-button (click)="deleteReport()">
                      {{ 'SystemAdmin.Components.CustomReportEditor.Tab.Basic.Form.Button.Delete' | translate }}
                    </button>

                  </ng-container>

                  <ng-template #existingReportActionTpl>

                    <button class="ml-2 frosty red flex items-center justify-center px-4 py-2 "
                      [matMenuTriggerFor]="menu" type="button">
                      <span class="whitespace-nowrap">
                        {{ 'SystemAdmin.Components.CustomReportEditor.Tab.Basic.Form.Button.Options' | translate }}
                      </span>
                      <fa-icon [icon]="['fas', 'chevron-down']" class="-mr-1 ml-2"></fa-icon>
                    </button>
                    <mat-menu #menu="matMenu" class="">
                      <button mat-menu-item (click)="discardChanges()">
                        <fa-icon [icon]="['fal', 'trash-arrow-up']" class="mr-2"></fa-icon>
                        <span> {{ 'SystemAdmin.Components.CustomReportEditor.Tab.Basic.Form.Button.DiscardChanges' |
                          translate}}</span>
                      </button>
                      <button class="text-red-500" mat-menu-item (click)="deleteReport()">
                        <fa-icon [icon]="['fal', 'trash']" class="mr-2"></fa-icon>
                        <span>{{ 'SystemAdmin.Components.CustomReportEditor.Tab.Basic.Form.Button.Delete' |
                          translate}}</span>
                      </button>
                    </mat-menu>

                  </ng-template>



                  <button class="ml-2 frosty blue" type="button" mat-flat-button (click)="goToNextTab()">
                    {{ 'SystemAdmin.Components.CustomReportEditor.Tab.Basic.Form.Button.Next' | translate }}
                  </button>
                </div>

              </div>

              <!-- Icon Ayarları Sekmesi -->
              <div *ngIf="currentTab === 1">

                <div class="grid grid-cols-1 lg:grid-cols-[1fr,1fr] gap-4 w-full">
                  <!-- Icon -->
                  <div class="w-full px-2">
                    <app-icon-selector formControlName="icon" [externalClass]="'frosty'"
                      label="{{'SystemAdmin.Components.CustomReportEditor.Tab.Icon.Form.Icon' | translate}}"></app-icon-selector>
                  </div>

                  <!-- IconColorCode -->
                  <div class="w-full px-2">
                    <mat-form-field class="w-full frosty" ngx-colors-trigger format="hex"
                      (change)="updateColor('iconColorCode', $event)">
                      <mat-label>{{ 'SystemAdmin.Components.CustomReportEditor.Tab.Icon.Form.Color' | translate
                        }}</mat-label>
                      <input matInput formControlName="iconColorCode" class="uppercase select-none cursor-pointer"
                        readonly="true" />
                      <fa-icon matSuffix [icon]="['fas', 'circle']" size="2x" class="select-none cursor-pointer"
                        [style.color]="formGroup.get('iconColorCode')?.value">
                      </fa-icon>
                    </mat-form-field>
                  </div>
                </div>

                <!-- Navigasyon Butonları -->
                <div class="flex mt-4 justify-between">
                  <button class="ml-2 frosty" type="button" mat-flat-button (click)="goToPreviousTab()">
                    {{ 'SystemAdmin.Components.CustomReportEditor.Tab.Icon.Form.Button.Back' | translate }}
                  </button>
                  <button class="ml-2 frosty blue" type="button" mat-flat-button (click)="goToNextTab()">
                    {{ 'SystemAdmin.Components.CustomReportEditor.Tab.Icon.Form.Button.Next' | translate }}
                  </button>
                </div>
              </div>

              <!-- Diğer Ayarlar Sekmesi -->
              <div *ngIf="currentTab === 2">
                <!-- IsPublic -->
                <div class="w-full px-2 mt-4 flex items-center">
                  <mat-slide-toggle formControlName="isPublic">
                    {{ 'SystemAdmin.Components.CustomReportEditor.Tab.Other.Form.IsPublic' | translate }}
                  </mat-slide-toggle>
                </div>

                <!-- Navigasyon Butonları -->
                <div class="flex mt-4 justify-between">
                  <div class="flex space-x-2">
                    <button class="ml-2 frosty" type="button" mat-flat-button (click)="goToPreviousTab()">
                      {{ 'SystemAdmin.Components.CustomReportEditor.Tab.Other.Form.Button.Back' | translate }}
                    </button>

                  </div>
                  <button type="submit" class="ml-2 frosty green" mat-flat-button>
                    {{ 'SystemAdmin.Components.CustomReportEditor.Tab.Other.Form.Button.Save' | translate }}
                  </button>
                </div>
              </div>
            </div>
          </form>
          <!-- Form Sonu -->
        </div>
      </div>
    </div>
  </div>
</div>

<pre>{{parse(reportModel?.gridState) | json}}</pre>