// src/app/custom-report-editor/custom-report-editor.component.ts

import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CustomReportEditor } from './models/custom-report-editor.model';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { SystemMenuService } from '@app/core/services/system-menu.services';
import { UtilityService } from '@app/modules/live/services/utility.service';
import { TranslateService } from '@ngx-translate/core';
import { LanguageService } from '@app/shared/services/language.service';
import { debounceTime } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CustomReportService } from './custom-report.service';
import { ActivatedRoute, Router } from '@angular/router';

@UntilD<PERSON>roy()
@Component({
  selector: 'app-custom-report-editor',
  templateUrl: './custom-report-editor.component.html',
  styleUrls: ['./custom-report-editor.component.scss'],
  host: { 'class': 'm-0 p-0' }
})
export class CustomReportEditorComponent implements OnInit {


  @Input() visible: boolean = false;

  private _reportModel: CustomReportEditor.CustomReport = {} as CustomReportEditor.CustomReport;


  @Input()
  get reportModel(): CustomReportEditor.CustomReport {
    return this._reportModel;
  }
  set reportModel(value: CustomReportEditor.CustomReport) {
    if (value) {
      this._reportModel = value;
      if (this.formGroup) {
        this.formGroup.patchValue(this._reportModel, { emitEvent: false });
        this.reportModelChange.emit(this._reportModel);

  
      }
    }
  }
  @Output() reportModelChange = new EventEmitter<CustomReportEditor.CustomReport>();
  @Output() isExpandedChange = new EventEmitter<boolean>();

  /**
   * Operation Result Output
   * - success: boolean indicating if the operation was successful
   * - action: 'create' | 'update' | 'delete' indicating the type of operation
   * - report: The CustomReport object involved in the operation (if applicable)
   * - error: Error information if the operation failed
   */
  @Output() operationResult = new EventEmitter<CustomReportEditor.OperationResult>();

  private _isExpanded: boolean = false;
  @Input()
  get isExpanded(): boolean {
    return this._isExpanded;
  }

  set isExpanded(value: boolean) {
    if (this._isExpanded !== value) {
      this._isExpanded = value;
      this.isExpandedChange.emit(this._isExpanded);
    }
  }

  formGroup: FormGroup;
  currentTab: number = 0;

  tabs: CustomReportEditor.TabModel[] = [];

  constructor(
    private _formBuilder: FormBuilder,
    private menuService: SystemMenuService,
    private utility: UtilityService,
    public translate: TranslateService,
    ls: LanguageService,
    private customReportService: CustomReportService,
    private snackBar: MatSnackBar) {

    this.initUI();

    this.formGroup = this._formBuilder.group({
      // Adım 1 Alanları
      id: [''],
      order: [0, [Validators.required, Validators.min(0)]],
      name: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(64)]],
      description: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(256)]],
      colorCode: ['#ffffff'],
      // Adım 2 Alanları
      icon: [''],
      iconColorCode: ['#ffffff'],
      // Adım 3 Alanları
      isPublic: [false],
    });

    this.operationResult.pipe(
      untilDestroyed(this)
    ).subscribe((result) => {
      if (result.success) {
        switch (result.action) {
          case 'create':
          case 'update':
            {
              if (result.success) {
                this.isExpanded = false;
                this.refreshCustomMenu();
              }
            }
            break;
          case 'delete': {
            if (result.success) {
              this.menuService.removeCustomReportMenuItem(this.reportModel.id);
              this.reportModel = {} as CustomReportEditor.CustomReport;
              this.refreshCustomMenu();
              this.isExpanded = false;
            }

          }
            break;
          case 'discard':
            {
              this.refreshCustomMenu();
            }
            break;
          default:
            break;
        }

      }


    });

    ls.languageChangedAndReady.pipe(
      debounceTime(1000),
      untilDestroyed(this)
    ).subscribe(() => {
      this.initUI();
    });

  }

  initUI() {
    this.tabs = [
      {
        label: this.translate.instant('SystemAdmin.Components.CustomReportEditor.Tab.Basic.Title'),
        icon: ['fal', 'circle-1'],
        doneIcon: ['fal', 'check-circle'],
        failIcon: ['fal', 'times-circle'],
        status: 'default',
      },
      {
        label: this.translate.instant('SystemAdmin.Components.CustomReportEditor.Tab.Icon.Title'),
        icon: ['fal', 'circle-2'],
        doneIcon: ['fal', 'check-circle'],
        failIcon: ['fal', 'times-circle'],
        status: 'default',
      },
      {
        label: this.translate.instant('SystemAdmin.Components.CustomReportEditor.Tab.Other.Title'),
        icon: ['fal', 'circle-3'],
        doneIcon: ['fal', 'check-circle'],
        failIcon: ['fal', 'times-circle'],
        status: 'default',
      },
    ];
  }

  ngOnInit() {
    if (this.reportModel) {
      this.formGroup.patchValue(this.reportModel, { emitEvent: false });
    }

    // Formdaki anlık değişiklikleri dinlemek için
    this.formGroup.valueChanges.pipe(
      untilDestroyed(this)
    ).subscribe((changes) => {
      this.onFormChanges(changes);
    });
  }
  onFormChanges(changes: any) {
    // Sadece property'leri güncelle, sınıf örneğini koru
    Object.assign(this.reportModel, changes);

    // Icon property’si için özel bir güncelleme
    this.reportModel.icon = this.utility.resolveIcon(this.reportModel.icon);

    // Güncellenmiş modeli yayınla ve önizleme yap
    this.reportModelChange.emit(this.reportModel);
    this.previewChanges(this.reportModel);
  }
  
  previewChanges(_reportModel: CustomReportEditor.CustomReport) {
    this.menuService.updateCustomReportMenuItem(_reportModel);
  }

  isTabValid(tabIndex: number): boolean {
    switch (tabIndex) {
      case 0:
        return (
          this.formGroup.get('order')?.valid &&
          this.formGroup.get('name')?.valid &&
          this.formGroup.get('description')?.valid
        );
      case 1:
        // Adım 2 için gerekli validasyonları burada kontrol edebilirsiniz
        return true; // Eğer herhangi bir zorunlu alan yoksa
      case 2:
        return true;
      default:
        return false;
    }
  }

  updateTabStatus(tabIndex: number) {
    if (this.isTabValid(tabIndex)) {
      this.tabs[tabIndex].status = 'done';
    } else {
      this.tabs[tabIndex].status = 'fail';
    }
  }

  canNavigateToTab(tabIndex: number): boolean {
    if (tabIndex === this.currentTab) {
      return true;
    } else if (tabIndex < this.currentTab) {
      return true; // Geri gitmeye izin veriyoruz
    } else {
      return false; // İleriye tıklayarak geçmek yasak
    }
  }

  goToTab(tabIndex: number) {
    if (this.canNavigateToTab(tabIndex)) {
      this.currentTab = tabIndex;
    }
  }

  goToNextTab() {
    // Mevcut tabın durumunu güncelle
    this.updateTabStatus(this.currentTab);

    if (this.isTabValid(this.currentTab)) {
      if (this.currentTab < this.tabs.length - 1) {
        this.currentTab++;
      }
    } else {
      // Hataları işaretle
      this.formGroup.markAllAsTouched();
    }
  }

  goToPreviousTab() {
    if (this.currentTab > 0) {
      this.currentTab--;
    }
  }

  submitForm() {
    // Son tabın durumunu güncelle
    this.updateTabStatus(this.currentTab);

    const closeMessage = this.translate.instant('SystemAdmin.Common.Form.Messages.Close');

    if (this.formGroup.valid) {
 
      if (this.reportModel.new) {
        // Yeni rapor ekle
        this.customReportService.addCustomReport(this.reportModel).pipe(
          untilDestroyed(this)
        ).subscribe({
          next: (newReport) => {
            this.snackBar.open(this.translate.instant('SystemAdmin.Components.CustomReportEditor.Messages.ReportAdded'), closeMessage, { duration: 3000 });

            this.operationResult.emit({
              success: true,
              action: 'create',
              report: newReport
            });

          },
          error: (err) => {

            this.snackBar.open(this.translate.instant('SystemAdmin.Components.CustomReportEditor.Messages.ReportAddError'), closeMessage, { duration: 3000 });
            this.operationResult.emit({
              success: false,
              action: 'create',
              report: this.reportModel,
              error: err
            });
          }
        });
      } else {
        // Var olan raporu güncelle
        this.customReportService.updateCustomReport(this.reportModel).pipe(
          untilDestroyed(this)
        ).subscribe({
          next: (updatedReport) => {
            this.snackBar.open(this.translate.instant('SystemAdmin.Components.CustomReportEditor.Messages.ReportUpdated'), closeMessage, { duration: 3000 });

            this.operationResult.emit({
              success: true,
              action: 'update',
              report: updatedReport
            });

          },
          error: (err) => {
            this.snackBar.open(this.translate.instant('SystemAdmin.Components.CustomReportEditor.Messages.ReportUpdateError'), closeMessage, { duration: 3000 });
            this.operationResult.emit({
              success: false,
              action: 'update',
              report: this.reportModel,
              error: err
            });
          }
        });
      }
    } else {
      // Form geçerli değil
      const errorMsg = this.translate.instant('SystemAdmin.Common.Form.Messages.FormInvalid');
      this.snackBar.open(errorMsg, closeMessage, { duration: 3000 });
      this.operationResult.emit({
        success: false,
        action: this.reportModel.id ? 'update' : 'create',
        report: this.reportModel,
        error: errorMsg
      });
      this.formGroup.markAllAsTouched();
    }
  }

  getTabIcon(tab: CustomReportEditor.TabModel): IconProp {
    if (tab.status === 'done') {
      return tab.doneIcon;
    } else if (tab.status === 'fail') {
      return tab.failIcon;
    } else {
      return tab.icon;
    }
  }

  // Discard changes
  discardChanges() {
    this.operationResult.emit({
      success: true,
      action: 'discard',
      report: this.reportModel,
    });
  }

  // Toggle expand/collapse
  toggleExpand() {
    this.isExpanded = !this.isExpanded;
    this.goToTab(0);
  }

  updateColor(property: string, newColor: any) {
    this.formGroup.get(property)?.setValue(newColor);
  }

  // Silme fonksiyonu
  deleteReport() {
    const closeMessage = this.translate.instant('SystemAdmin.Common.Messages.Close');

    if (this.reportModel.new) {
      // Yeni rapor sunucuda olmadığı için silme işlemi yapmadan success döndür
      this.snackBar.open(this.translate.instant('SystemAdmin.Components.CustomReportEditor.Messages.ReportDeleted'), closeMessage, { duration: 3000 });
      this.operationResult.emit({
        success: true,
        action: 'delete',
        report: this.reportModel
      });
    } else if (this.reportModel.id) {
      if (confirm(this.translate.instant('SystemAdmin.Components.CustomReportEditor.Messages.ConfirmDelete'))) {
        this.customReportService.deleteCustomReport(this.reportModel.id).pipe(
          untilDestroyed(this)
        ).subscribe({
          next: () => {
            this.snackBar.open(this.translate.instant('SystemAdmin.Components.CustomReportEditor.Messages.ReportDeleted'), closeMessage, { duration: 3000 });
            this.operationResult.emit({
              success: true,
              action: 'delete',
              report: this.reportModel
            });

          },
          error: (err) => {
            this.snackBar.open(this.translate.instant('SystemAdmin.Components.CustomReportEditor.Messages.ReportDeleteError'), closeMessage, { duration: 3000 });
            this.operationResult.emit({
              success: false,
              action: 'delete',
              report: this.reportModel,
              error: err
            });
          }
        });
      }
    } else {
      const errorMsg = this.translate.instant('SystemAdmin.Components.CustomReportEditor.Messages.ReportDeleteNoId');
      this.snackBar.open(errorMsg, closeMessage, { duration: 3000 });
      this.operationResult.emit({
        success: false,
        action: 'delete',
        report: this.reportModel,
        error: errorMsg
      });
    }
  }




  refreshCustomMenu() {

    this.menuService.refreshCustomReportsMenu();

    if (this.reportModel?.id)
      this.reportModel = this.menuService.getCustomReportMenuItem(this.reportModel.id);


    this.operationResult.emit({
      success: true,
      action: 'refresh',
      report: this.reportModel
    });

  }

  parse(data:any):any
  {
    if (!data) return null;
    return JSON.parse(data);
  }

}