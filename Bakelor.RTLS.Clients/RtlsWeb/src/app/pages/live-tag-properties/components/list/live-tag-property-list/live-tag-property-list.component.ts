
import { LiveTagPropertyDataService } from '../../../services/live-tag-property-data.service';
import { Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Guid } from '@app/core/constants/constants';
import { RecordState, ValueType } from '@app/core/constants/global-enum';
import { AlertHubConnectionService } from '@app/core/services/hubs/alert-hub-connection.service';
import { LiveTagPropertyDTOs } from '@app/pages/live-tag-properties/dtos/live-tag-property';
import { TagPropertyTemplateDataService } from '@app/pages/tag-property-templates/services/tag-property-template-data.service';

import { BaseComponent } from '@app/shared/components/base/base.component';
import { CustomReportEditor } from '@app/shared/components/custom-report-editor/models/custom-report-editor.model';
import { ViewType } from '@app/shared/components/list-page-base/models/common';
import { DrawerService } from '@app/shared/services/drawer.service';
import { ImageService } from '@app/shared/services/image.service';
import { OperationType } from '@app/shared/services/models/operation-result';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import dayjs from 'dayjs';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import { SystemMenuService } from '@app/core/services/system-menu.services';
import { DxDataGridComponent } from 'devextreme-angular';
import { NorthernColors } from '@app/shared/components/northern-lights/models';
import { BadisOauthLoginService } from '@app/core/services/auth/badis-oauth-login.service';

@UntilDestroy()
@Component({
  selector: 'app-live-tag-property-list',
  templateUrl: './live-tag-property-list.component.html',
  styleUrls: ['./live-tag-property-list.component.scss']
})

export class LiveTagPropertyListComponent extends BaseComponent implements OnInit, OnDestroy {


  isRelevantChange: boolean = false;
  // Properties
  reportModel: CustomReportEditor.CustomReport;

  private _customReportId: string;
  customEditorIsExpanded: boolean;

  get customReportId(): string {
    return this._customReportId;
  }

  set customReportId(value: string) {
    this._customReportId = value;
    this.loadReport(this._customReportId);
  }


  tableName: string = "Tracking Report";
 

  valueTypes = ValueType;

  viewTypes = ViewType;
  selectedItem: LiveTagPropertyDTOs.LiveTagProperty;
  selectedRows: any[] = [];
  expandGridRows: boolean = false;
  enableCalendarDate: boolean = false;

  _virtualScroll: boolean = false;
  get virtualScroll(): boolean {
    return this._virtualScroll;
  }

  set virtualScroll(value: boolean) {
    this._virtualScroll = value;
    if (this._virtualScroll) {
      this.expandGridRows = true;
    } else {
      this.expandGridRows = false;
    }
  }

  @ViewChild(DxDataGridComponent, { static: false }) dataGrid: DxDataGridComponent;

  pageSize: number = 10;
  pageIndex: number = 0;
  allowedPageSizes: number[] = [10, 20, 50, 100];
  searchText: string;

  NorthernColors = NorthernColors;

  constructor(
    public liveTagPropertyDataService: LiveTagPropertyDataService,
    public drawerService: DrawerService<LiveTagPropertyDTOs.LiveTagProperty>,
    private activatedRoute: ActivatedRoute,
    public imageService: ImageService,
    public alertHub: AlertHubConnectionService,
    public tagPropertyTemplateService: TagPropertyTemplateDataService,
    public menuService: SystemMenuService,
    public oauth: BadisOauthLoginService) {
    super();

    this.liveTagPropertyDataService.operationCompleted.pipe(untilDestroyed(this)).subscribe((data) => {
      switch (data.operationType) {
        case OperationType.create:
        case OperationType.update:
        case OperationType.delete:
        default:
          this.dataGrid.instance.refresh();
          break;
      }
    });


    this.alertHub.alertUpdated$.pipe(untilDestroyed(this)).subscribe(async (data) => {
      if (data) {
      }
    });

  }

  ngOnInit(): void {
    super.ngOnInit();

    this.liveTagPropertyDataService.initDataSource('id', { recordStates: [RecordState.Active/*, RecordState.Passive*/] });


    this.activatedRoute.queryParams.pipe(untilDestroyed(this)).subscribe(async params => {
      const childTagId: Guid = params['child-tag'];

      //validate guid
      if (!childTagId) {
        return;
      }


    });

  }

  public selectedItemChange(item: LiveTagPropertyDTOs.LiveTagProperty): void {
    return;
    if (!item) { return; }

    this.selectedItem = item;

    this.router.navigate(['./', this.selectedItem.id], { relativeTo: this.activatedRoute });
    this.drawerService.open(this.selectedItem, this.drawerService.actionMode);

  }



  viewChanged(val: ViewType): void {
    this.log.trace('view changed: ', val);
  }





  refreshDataGrid() {
    this.dataGrid.instance.refresh();
  }
  toggleGroupRow(groupInfo: any) {
    if (groupInfo.row.isExpanded) {
      groupInfo.component.collapseRow(groupInfo.row.key);
    } else {
      groupInfo.component.expandRow(groupInfo.row.key);
    }
  }
  exportExcel(): void {
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet(this.tableName);

    exportDataGrid({
      component: this.dataGrid.instance,
      worksheet: worksheet,
      autoFilterEnabled: true,
      topLeftCell: { row: 1, column: 1 },
      customizeCell: (options) => {
        const { gridCell, excelCell } = options;

        // if(gridCell.rowType === 'data') {
        //     excelCell.font = { color: { argb: 'FF0000FF' }, underline: true };
        //     excelCell.alignment = { horizontal: 'left' };
        // }
      }
    }).then(() => {
      workbook.xlsx.writeBuffer().then((buffer: BlobPart) => {
        const date = dayjs().format('DD.MM.YYYY HH.mm');
        const tableNameSanitized = this.tableName.replace(/[^a-z0-9]/gi, ' ');
        const fileName = `${tableNameSanitized} ${date}.xlsx`;
        saveAs(new Blob([buffer], { type: 'application/octet-stream' }), fileName);
      });
    });
  }

  discardChanges() {
    this.dataGrid.instance.state(null);
    this.dataGrid.instance.refresh();
  }


  gridOptionChanged(event: any): void {
    this.log.trace('gridOptionChanged: ', event.fullName);
    const relevantChanges = ['filterEnabled', 'filter', 'filterValue', 'filterValues', 'selectedFilterOperation', 'filterType', 'sort', 'sortOrder', 'group', 'groupIndex', 'visible', 'visibleIndex', 'width'];
    
    this.isRelevantChange = true;

    if (this.reportModel?.new)
      this.isRelevantChange = true;

    if (this.isRelevantChange) {

      if (this.reportModel) {
        this.reportModel.gridState = this.dataGrid.instance.state();
      }
    }
    

  }


  loadReport(customReportId: Guid): void {
    const report = this.menuService.getCustomReportMenuItem(customReportId);

    this.isRelevantChange = !!report?.new;
    this.customEditorIsExpanded = this.isRelevantChange;

    if (report) {
      this.dataGrid.instance.state(report.gridState);
      this.reportModel = report;
    } else {
      this.dataGrid.instance.state(null);
      this.reportModel = null;
    }

    this.dataGrid.instance.refresh();
  }




  customReportEditorIsExpanded(isExpanded: boolean): void {
    this.customEditorIsExpanded = isExpanded;
    if (isExpanded === undefined || !isExpanded) return;

    const isUserAuthorized = this.oauth.userAccessToken.userId === this.reportModel?.createdById;
    let newReport: CustomReportEditor.CustomReport;

    if (!this.reportModel) {
      // Yeni bir rapor oluştur
      newReport = new CustomReportEditor.CustomReport();
      newReport.new = true;
      newReport.id = Guid.newGuid();
      newReport.code = `CRS-${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`;
      newReport.groupKey = 'Sensor';
      newReport.gridState = this.dataGrid.instance.state();
    } else if (!isUserAuthorized && !this.reportModel.new) {
      // Kullanıcı yetkili değilse ve rapor yeni değilse kopyasını oluştur
      newReport = Object.assign(new CustomReportEditor.CustomReport(), this.reportModel);
      newReport.id = Guid.newGuid();
      newReport.new = true;
      newReport.gridState = this.dataGrid.instance.state();

      // İsme ' - Copy' ekle
      if (!newReport.name.endsWith(' - Copy')) {
        newReport.name += ' - Copy';
      }
    }

    // Eğer yeni bir rapor veya kopya oluşturulduysa, menüye ekle ve yönlendir
    if (newReport) {
      this.menuService.addCustomReportMenuItem(newReport);
      this.router.navigate(['.', newReport.id], { relativeTo: this.activatedRoute });
    }
  }

  customReportEditonOperationResultChanged($event: CustomReportEditor.OperationResult) {

    if ($event.action === 'refresh') {
      this.loadReport($event.report.id);
    }
  }



  get state(): any
  {
    return this.dataGrid?.instance?.state();
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
  }
}