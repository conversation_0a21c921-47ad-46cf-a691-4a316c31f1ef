<app-list-page-base [customHeader]="true" [customHeaderAction]="false" [title]="'Live Tag Property Page'"
    [icon]="['fal','rectangle-history']" [description]="'Custom Live Tag Property Description'"
    [supportedViews]="viewTypes.table" [currentView]="viewTypes.table" (currentViewChange)="viewChanged($event)"
    (resolvedId)="customReportId=$event" (searchTermChange)="searchText = $event" [hideAdd]="true" #listPageBase>


    <!-- Header -->
    <ng-container header>

        <fa-icon [icon]="reportModel?.icon??['fal', 'layer-group']" size="4x" class="text-slate-200"></fa-icon>

        <div class="flex flex-col min-w-0 ml-4">
            <ng-container>
                <div
                    class="text-2xl md:text-5xl font-semibold text-slate-100 tracking-tight leading-7 md:leading-snug truncate">
                    <!-- Check if reportModel.title exists, else fallback to translated string -->
                    <span class="relative" *ngIf="reportModel?.name; else defaultName">
                        {{ reportModel.name }}
                        <!-- Alert/Alarm icon next to the title -->
                        <fa-icon *ngIf="!reportModel?.isPublic"
                            class="absolute top-4 -right-2 translate-x-1/2 -translate-y-1/2 text-md text-yellow-500"
                            [icon]="['far', 'lock-keyhole']" size="1x">
                        </fa-icon>
                    </span>
                    <ng-template #defaultName>
                        <span class="relative">
                            {{'LiveTagProperty.List.Header.Title' | translate}}
                            <!-- Alert/Alarm icon next to the default title -->
                            <fa-icon *ngIf="!reportModel?.isPublic"
                                class="absolute top-4 -right-2 translate-x-1/2 -translate-y-1/2 text-md text-yellow-500"
                                [icon]="['far', 'lock-keyhole']" size="1x">
                            </fa-icon>
                        </span>
                    </ng-template>
                </div>
            </ng-container>
            <div class="flex items-center">

                <div class="ml-1.5 leading-6 truncate  text-slate-200">
                    <!-- Check if reportModel.title exists, else fallback to translated string -->
                    <span *ngIf="reportModel?.description; else defaultDescription">{{
                        reportModel.description }}</span>
                    <ng-template #defaultDescription>{{'LiveTagProperty.List.Header.Description'
                        |translate}}</ng-template>
                </div>
            </div>
        </div>


    </ng-container>

    <!-- Header Action-->
    <ng-container headerAction>

    </ng-container>



    <!-- Content -->
    <ng-container tableContent>

        <div northern-lights class="frosty mb-4" [color]="NorthernColors.None"
            [flash]="reportModel?.gridStateChanged && customEditorIsExpanded" [flashColor]="NorthernColors.Blue">

            <div class="flex flex-col space-y-0 ">

                <!-- Row 1: Original Grid Content -->
                <div class="flex flex-wrap">
                    <!-- Diğer filtreler veya seçim kutuları -->
                    <div class="w-full sm:w-1/2 md:w-1/4 lg:w-1/4 xl:w-1/4 p-4">
                        <!-- Filtreler veya seçim kutuları eklenebilir -->
                    </div>
                    <div class="w-full sm:w-1/2 md:w-1/4 lg:w-1/4 xl:w-1/4 p-4">
                        <!-- Tarih seçim kutuları veya diğer öğeler -->
                    </div>
                    <div class="w-full sm:w-1/2 md:w-1/4 lg:w-1/4 xl:w-1/4 p-4">
                        <!-- Diğer filtreler veya seçim kutuları -->
                    </div>
                    <div class="w-full sm:w-1/2 md:w-1/4 lg:w-1/4 xl:w-1/4 p-4">
                        <div class="text-right">
                            <div class="inline-flex min-h-10 " role="group">
                                <app-toggle-button [(ngModel)]="virtualScroll" position="left"
                                    [onIcon]="['fas','infinity']" [offIcon]="['fal','infinity']"
                                    tooltip="{{'SystemAdmin.Common.Report.List.Header.Toolbar.InfiniteScroll' | translate}}">
                                </app-toggle-button>

                                <app-toggle-button [(ngModel)]="expandGridRows" [disabled]="virtualScroll"
                                    position="center" [onIcon]="['fas','chevron-down']"
                                    [offIcon]="['fal','chevron-right']"
                                    tooltip="{{'SystemAdmin.Common.Report.List.Header.Toolbar.ExpandGroups' | translate}}">
                                </app-toggle-button>

                                <!-- Enable Calendar Date -->
                                <app-toggle-button [(ngModel)]="enableCalendarDate" position="center"
                                    [onIcon]="['fas','calendar-alt']" [offIcon]="['fal','calendar-alt']"
                                    tooltip="{{'SystemAdmin.Common.Report.List.Header.Toolbar.CalendarDate' | translate}}">
                                </app-toggle-button>

                                <app-toggle-button position="right" [offIcon]="['fal','floppy-disk']" [isToggle]="false"
                                    (valueChange)="exportExcel()"
                                    tooltip="{{'SystemAdmin.Common.Report.List.Header.Toolbar.ExportToExcel' | translate}}">
                                </app-toggle-button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Row 2: Notification Row -->
                <app-custom-report-editor [visible]="isRelevantChange" [(reportModel)]="reportModel"
                    [isExpanded]="customEditorIsExpanded" (isExpandedChange)="customReportEditorIsExpanded($event)"
                    (operationResult)="customReportEditonOperationResultChanged($event)">
                </app-custom-report-editor>

            </div>
        </div>

        <div class="frosty m-3 p-4">
            <div class="flex flex-row space-x-4">
                <div class="w-1/2">
                    <h3 class="text-sm font-semibold mb-2">Report Model Grid State:</h3>
                    <pre
                        class="bg-slate-100 p-3 rounded-lg text-xs overflow-auto ">{{reportModel?.gridState | json}}</pre>
                </div>
                <div class="w-1/2">
                    <h3 class="text-sm font-semibold mb-2">Current State:</h3>
                    <pre class="bg-slate-100 p-3 rounded-lg text-xs overflow-auto ">{{state | json}}</pre>
                </div>
            </div>
        </div>

        <ng-container *ngrxLet="tagPropertyTemplateService.lookUpCache$ | async as lookUpCache">

            <!-- Data Grid -->
            <div id="table-container"
                class="flex flex-col flex-auto shadow rounded-2xl overflow-hidden frosty thick-ice">

                <dx-data-grid #myGrid [dataSource]="liveTagPropertyDataService.dataSource" [showColumnLines]="true"
                    [showRowLines]="true" [columnHidingEnabled]="true" [showBorders]="false"
                    [allowColumnReordering]="true" [allowColumnResizing]="true" [focusedRowEnabled]="false"
                    [hoverStateEnabled]="false" [rowAlternationEnabled]="true" [columnAutoWidth]="true"
                    [remoteOperations]="true" (onOptionChanged)="gridOptionChanged($event)" (filterValueChange)="gridOptionChanged($event)">

                    <!-- Grid State Storing -->


                    <!-- Toolbar -->
                    <dxo-toolbar>
                        <dxi-item location="before" name="groupPanel"></dxi-item>
                        <dxi-item location="after">
                            <div *dxTemplate>
                                <dx-button icon="refresh" (onClick)="refreshDataGrid()">
                                </dx-button>
                            </div>
                        </dxi-item>
                        <dxi-item location="after" name="columnChooserButton"></dxi-item>
                    </dxo-toolbar>

                    <!-- Load Panel -->
                    <dxo-load-panel [enabled]="true" [showIndicator]="true" [showPane]="true" [shading]="false"
                        shadingColor="rgba(0,0,0,0.3)">
                        <dxo-position my="center" at="center" of="#table-container">
                        </dxo-position>
                    </dxo-load-panel>

                    <!-- Grid Features -->
                    <dxo-sorting mode="multiple"></dxo-sorting>

                    <dxo-group-panel [visible]="true"></dxo-group-panel>
                    <dxo-grouping [autoExpandAll]="expandGridRows" [contextMenuEnabled]="true"></dxo-grouping>
                    <dxo-column-chooser [enabled]="true" mode="dragAndDrop"></dxo-column-chooser>
                    <dxo-keyboard-navigation [enabled]="true"></dxo-keyboard-navigation>
                    <dxo-search-panel [visible]="false" [width]="240" [text]="searchText"></dxo-search-panel>

                    <!-- Filter -->
                    <dxo-filter-row [visible]="true" [applyFilter]="'Immediately'"></dxo-filter-row>
                    <dxo-header-filter [visible]="true" [allowSearch]="true"></dxo-header-filter>
                    <dxo-filter-panel [visible]="true"></dxo-filter-panel>

                    <dxo-filter-builder-popup>
                    </dxo-filter-builder-popup>
                    <dxo-filter-builder [allowHierarchicalFields]="true">
                    </dxo-filter-builder>

                    <!-- Scrolling and Paging -->
                    <dxo-scrolling *ngIf="virtualScroll" mode="infinite" rowRenderingMode="virtual"></dxo-scrolling>
                    <dxo-paging [pageSize]="pageSize" [pageIndex]="pageIndex"> </dxo-paging>
                    <dxo-pager [visible]="!virtualScroll" [allowedPageSizes]="allowedPageSizes" [displayMode]="'full'"
                        [showPageSizeSelector]="true" [showInfo]="false" [showNavigationButtons]="false">
                    </dxo-pager>

                    <!-- Columns -->

                    <!-- TrackItem -->
                    <dxi-column [visibleIndex]="1" dataField="trackItemDescription" dataType="string"
                        [sortOrder]="'asc'" cellTemplate="trackItemDescriptionTemplate"
                        caption="{{'LiveTagProperty.List.Table.Header.Description'  | translate }}">
                    </dxi-column>

                    <div *dxTemplate="let data of 'trackItemDescriptionTemplate'">

                        <div class="flex flex-col">
                            <div class="flex items-center">
                                <div class="text-center">
                                    <div
                                        class="relative w-10 h-10 rounded-full border-white border-opacity-50 border-2 overflow-hidden transition-transform duration-300 hover:scale-125 ease-in-out mr-2">
                                        <img [src]="imageService.temporaryTrackItemImage(data.data.trackItemPhotoUrl)"
                                            (error)="imageService.imageError($event)" alt="Fotoğraf"
                                            class="object-cover object-center w-full h-full max-w-xs hover:scale-150  origin-center transition duration-1000 ease-in-out z-0">
                                    </div>
                                </div>

                                <div class="text-left">
                                    <div class="text-sm text-black font-medium">
                                        <a [routerLink]="['/track-items', data.data.trackItemId]" target="_blank">
                                            {{data.data.trackItemDescription}}
                                        </a>
                                    </div>

                                    <div class="text-xs text-slate-500 font-normal">
                                        {{data.data.trackItemTypeName}} ▸
                                        <a [routerLink]="['/tags', data.data.tagId]" target="_blank">
                                            {{data.data.tagName}}
                                        </a>
                                    </div>

                                    <div class="text-xs text-slate-700 font-normal ">
                                        <!-- Ek bilgi ekleyebilirsiniz -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Group -->
                    <dxi-column [visibleIndex]="2" [hidingPriority]="2" dataField="groupName" dataType="string"
                        [visible]="false" caption="{{'LiveTagProperty.List.Table.Header.Group' | translate}}"
                        alignment="right" [allowFiltering]="true">
                    </dxi-column>

                    <!-- Zone -->
                    <dxi-column [visibleIndex]="3" [hidingPriority]="3" dataField="zoneName" [visible]="false"
                        dataType="string" [width]="200" cellTemplate="zoneCellTemplate"
                        caption="{{'LiveTagProperty.List.Table.Header.Zone' | translate}}" [allowFiltering]="true">
                    </dxi-column>

                    <div *dxTemplate="let data of 'zoneCellTemplate'">
                        <div class="flex flex-col">
                            <div class="text-sm text-black font-medium flex justify-between">
                                <div class="flex flex-col justify-center">
                                    <a [routerLink]="['/zones', data.data.zoneId]" target="_blank">
                                        {{data.data.zoneName}}
                                    </a>
                                    <div class="text-xs text-slate-400 font-normal">
                                        <a [routerLink]="['/buildings', data.data.buildingId]"
                                            target="_blank">{{data.data.buildingName}}</a> ▸
                                        <a [routerLink]="['/floors', data.data.floorId]"
                                            target="_blank">{{data.data.floorName}}</a>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <!-- <a [routerLink]="['/live']"
                                        [queryParams]="{locate:'tag',id:data.data.tagSessionHistoryId}" target="_blank">
                                        <fa-icon [icon]="['fal', 'map-marker-alt']"
                                            class="icon-size-5 opacity-50"></fa-icon>
                                    </a> -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Live Zone -->
                    <dxi-column [visibleIndex]="4" [hidingPriority]="4" dataField="liveZoneName" dataType="string"
                        [width]="200" cellTemplate="liveZoneCellTemplate"
                        caption="{{'LiveTagProperty.List.Table.Header.LiveZone' | translate}}" [allowFiltering]="true">
                    </dxi-column>

                    <div *dxTemplate="let data of 'liveZoneCellTemplate'">
                        <div class="flex flex-col">
                            <div class="text-sm text-black font-medium flex justify-between">
                                <div class="flex flex-col justify-center">
                                    <a [routerLink]="['/zones', data.data.liveZoneId]" target="_blank">
                                        {{data.data.liveZoneName}}
                                    </a>
                                    <div class="text-xs text-slate-400 font-normal">
                                        <a [routerLink]="['/buildings', data.data.liveBuildingId]"
                                            target="_blank">{{data.data.liveBuildingName}}</a> ▸
                                        <a [routerLink]="['/floors', data.data.liveFloorId]"
                                            target="_blank">{{data.data.liveFloorName}}</a>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <a [routerLink]="['/live']"
                                        [queryParams]="{locate:'liveZone',id:data.data.liveZoneId}" target="_blank">
                                        <fa-icon [icon]="['fal', 'map-marker-alt']"
                                            class="icon-size-5 opacity-50"></fa-icon>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- Read At -->
                    <dxi-column [visibleIndex]="50" [hidingPriority]="1" dataField="readAt" dataType="date"
                        caption="{{'SystemAdmin.Components.TableBase.Header.ReadAt' | translate}}" alignment="right"
                        cellTemplate="readAtCellTemplate" [allowFiltering]="true">
                    </dxi-column>

                    <div *dxTemplate="let d of 'readAtCellTemplate'">
                        <ng-container *ngIf="!enableCalendarDate; else calendarDateTemplate">
                            {{d.data.readAt | datetimeFormat:'L LTS' }}
                        </ng-container>
                        <ng-template #calendarDateTemplate>
                            {{d.data.readAt| calendarTime:{sameElse:'L LTS'} }}
                        </ng-template>
                    </div>

                    <!-- Eklediğiniz Gizli Sütunlar -->
                    <!-- Aşağıdaki sütunlar gizli olarak ayarlanmıştır ve 'Id' ile bitenler eklenmemiştir -->

                    <!-- currentTagSessionHistoryId, historyId, readerAntennaId, zoneId, floorId, buildingId, tagId, tagTypeId, trackItemId gibi 'Id' ile biten özellikler eklenmedi -->


                    <!-- Read At Ago Value -->
                    <dxi-column [visibleIndex]="12" [visible]="false" dataField="readAtAgo" dataType="number"
                        caption="{{'LiveTagProperty.List.Table.Header.ReadAtAgo' | translate}}">
                    </dxi-column>


                    <!-- firstSeenAt (Zaten ekli, tekrar eklemeye gerek yok) -->

                    <!-- lastSeenAt (Zaten ekli, tekrar eklemeye gerek yok) -->

                    <!-- floorName -->
                    <dxi-column dataField="floorName" dataType="string" [visible]="false" [showWhenGrouped]="false"
                        caption="{{'LiveTagProperty.List.Table.Header.FloorName' | translate}}">

                        <div *dxTemplate="let groupInfo of 'groupCellTemplate'">

                            <div class="flex items-center p-2  cursor-pointer select-none"
                                (click)="toggleGroupRow(groupInfo)">

                                <div class="w-12 h-12 flex items-center justify-center bg-sky-100 rounded-lg">
                                    <fa-icon [icon]="['fal', 'layer-group']" class="text-sky-600 " size="2x"></fa-icon>
                                </div>

                                <div class="ml-4">
                                    <div class="text-lg font-semibold">
                                        {{ groupInfo.key }}
                                    </div>
                                    <div class="divide-x-2"></div>
                                    <div class="text-sm text-sky-600">
                                        {{ groupInfo.row.data.summary }} öğe
                                    </div>
                                </div>

                                <div *ngIf="false" class="ml-auto text-right">
                                    <fa-icon
                                        [icon]="groupInfo.row.isExpanded ? ['fas', 'chevron-up'] : ['fas', 'chevron-down']"
                                        class="text-gray-600"></fa-icon>
                                </div>
                            </div>
                        </div>
                    </dxi-column>

                    <!-- buildingName -->
                    <dxi-column dataField="buildingName" dataType="string" [visible]="false"
                        caption="{{'LiveTagProperty.List.Table.Header.BuildingName' | translate}}">
                    </dxi-column>


                    <!-- liveFloorName -->
                    <dxi-column dataField="liveFloorName" dataType="string" [visible]="false" [showWhenGrouped]="false"
                        caption="{{'LiveTagProperty.List.Table.Header.LiveFloorName' | translate}}">

                        <div *dxTemplate="let groupInfo of 'groupCellTemplate'">

                            <div class="flex items-center p-2 cursor-pointer select-none"
                                (click)="toggleGroupRow(groupInfo)">

                                <div class="w-12 h-12 flex items-center justify-center bg-green-100 rounded-lg">
                                    <fa-icon [icon]="['fal', 'layer-group']" class="text-green-600" size="2x"></fa-icon>
                                </div>

                                <div class="ml-4">
                                    <div class="text-lg font-semibold">
                                        {{ groupInfo.key }}
                                    </div>
                                    <div class="divide-x-2"></div>
                                    <div class="text-sm text-green-600">
                                        {{ groupInfo.row.data.summary }} öğe
                                    </div>
                                </div>

                                <div *ngIf="false" class="ml-auto text-right">
                                    <fa-icon
                                        [icon]="groupInfo.row.isExpanded ? ['fas', 'chevron-up'] : ['fas', 'chevron-down']"
                                        class="text-gray-600"></fa-icon>
                                </div>
                            </div>
                        </div>
                    </dxi-column>

                    <!-- liveBuildingName -->
                    <dxi-column dataField="liveBuildingName" dataType="string" [visible]="false"
                        caption="{{'LiveTagProperty.List.Table.Header.LiveBuildingName' | translate}}">
                    </dxi-column>


                    <!-- tagName -->
                    <dxi-column dataField="tagName" dataType="string" [visible]="false"
                        caption="{{'LiveTagProperty.List.Table.Header.TagName' | translate}}">
                    </dxi-column>

                    <!-- tagTypeName -->
                    <dxi-column dataField="tagTypeName" dataType="string" [visible]="false"
                        caption="{{'LiveTagProperty.List.Table.Header.TagTypeName' | translate}}">
                    </dxi-column>

                    <!-- trackItemTypeName -->
                    <dxi-column dataField="trackItemTypeName" dataType="string" [visible]="false"
                        caption="{{'LiveTagProperty.List.Table.Header.TrackItemTypeName' | translate}}">


                    </dxi-column>


                    <!-- Tag Template -->
                    <dxi-column [visibleIndex]="6" dataField="tagPropertyTemplateName" dataType="string"
                        cellTemplate="tagPropertyTemplateNameTemplate"
                        caption="{{'LiveTagProperty.List.Table.Header.TagPropertyTemplateName' | translate}}">
                    </dxi-column>
                    <div *dxTemplate="let tpt of 'tagPropertyTemplateNameTemplate'">

                        <ng-container *ngIf="tpt.data.tagPropertyTemplateId; let tagPropertyTemplateId">

                            <ng-container *ngIf="lookUpCache[tagPropertyTemplateId]; let pt">

                                <div class="flex flex-col">



                                    <div class="flex justify-between">

                                        <div class="flex flex-col justify-center">

                                            <div class="flex items-center">
                                                <div class="text-left w-9">
                                                    <fa-icon [icon]="pt.icon??['fal','caret-right']"
                                                        class="text-2xl"></fa-icon>
                                                </div>

                                                <div class="text-left">
                                                    <div class="text-sm text-black font-semibold">
                                                        <a [routerLink]="['/tag-property-templates',tpt.data.tagPropertyTemplateId]"
                                                            target="_blank">
                                                            {{tpt.data.tagPropertyTemplateName}}
                                                        </a>
                                                    </div>

                                                    <div class="text-xs text-slate-400">
                                                        {{pt.querySelector}}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="flex justify-between">

                                            <ng-container *ngrxLet="lookUpCache[tpt.data.tagPropertyTemplateId] as pt">

                                                <div *ngIf="pt">

                                                    <span>{{pt.prefix}}</span>
                                                    <ng-container [ngSwitch]="pt.valueType">
                                                        <ng-container
                                                            *ngSwitchCase="valueTypes.String">{{tpt.data.value}}</ng-container>
                                                        <ng-container
                                                            *ngSwitchCase="valueTypes.Number">{{tpt.data.numberValue}}</ng-container>
                                                        <ng-container *ngSwitchCase="valueTypes.Boolean">{{
                                                            tpt.data.booleanValue
                                                            }}</ng-container>
                                                        <ng-container *ngSwitchCase="valueTypes.DateTime">{{
                                                            tpt.data.dateTimeValue}}</ng-container>
                                                        <ng-container *ngSwitchDefault> There is no value type
                                                            :{{pt.valueType}}</ng-container>
                                                    </ng-container>
                                                    <span>{{pt.suffix}}</span>
                                                </div>

                                            </ng-container>

                                        </div>



                                    </div>
                                </div>
                            </ng-container>

                        </ng-container>


                    </div>



                    <!-- Tag Property Value -->
                    <dxi-column [visibleIndex]="7" [visible]="false" dataField="value" dataType="string"
                        caption="{{'LiveTagProperty.List.Table.Header.TextValue' | translate}}">
                    </dxi-column>

                    <!-- Tag Property Number Value -->
                    <dxi-column [visibleIndex]="8" [visible]="false" dataField="numberValue" dataType="number"
                        caption="{{'LiveTagProperty.List.Table.Header.NumberValue' | translate}}">
                    </dxi-column>

                    <!-- Tag Property Boolean Value -->
                    <dxi-column [visibleIndex]="9" [visible]="false" dataField="booleanValue" dataType="boolean"
                        caption="{{'LiveTagProperty.List.Table.Header.BooleanValue' | translate}}">
                    </dxi-column>

                    <!-- Tag Property Date Value -->
                    <dxi-column [visibleIndex]="10" [visible]="false" dataField="dateTimeValue" dataType="date"
                        caption="{{'LiveTagProperty.List.Table.Header.DateTimeValue' | translate}}">
                    </dxi-column>


                    <dxi-column [visibleIndex]="6" [hidingPriority]="2" [visible]="false" alignment="center"
                        dataType="string" caption="{{'LiveTagProperty.List.Table.Header.Value' | translate}}"
                        cellTemplate="tagPropertyTemplateCombinedValueTemplate">
                    </dxi-column>

                    <div *dxTemplate="let tpt of 'tagPropertyTemplateCombinedValueTemplate'">
                        <ng-container *ngrxLet="lookUpCache[tpt.data.tagPropertyTemplateId] as pt">

                            <div *ngIf="pt">

                                <span>{{pt.prefix}}</span>
                                <ng-container [ngSwitch]="pt.valueType">
                                    <ng-container *ngSwitchCase="valueTypes.String">{{tpt.data.value}}</ng-container>
                                    <ng-container
                                        *ngSwitchCase="valueTypes.Number">{{tpt.data.numberValue}}</ng-container>
                                    <ng-container *ngSwitchCase="valueTypes.Boolean">{{ tpt.data.booleanValue
                                        }}</ng-container>
                                    <ng-container *ngSwitchCase="valueTypes.DateTime">{{
                                        tpt.data.dateTimeValue}}</ng-container>
                                    <ng-container *ngSwitchDefault> There is no value type
                                        :{{pt.valueType}}</ng-container>
                                </ng-container>
                                <span>{{pt.suffix}}</span>
                            </div>

                        </ng-container>
                    </div>

                    <!-- Summaries -->
                    <dxo-summary>
                        <dxi-total-item column="trackItemDescription" summaryType="count" valueFormat=",##0.###">
                        </dxi-total-item>

                        <dxi-group-item column="trackItemDescription" summaryType="count" [alignByColumn]="true">
                        </dxi-group-item>

                    </dxo-summary>

                </dx-data-grid>
            </div>
        </ng-container>
    </ng-container>

    <ng-container drawer>
        <router-outlet></router-outlet>
    </ng-container>

    <ng-container cardContent>
        <!-- Diğer içerikler buraya eklenebilir -->
    </ng-container>

    <ng-container historyContent>
        <p class="px-6 py-6">History content goes here</p>
    </ng-container>

    <!-- Footer -->
    <ng-container footer>

    </ng-container>
</app-list-page-base>